module.exports = {
  /**
   * 获取门店分析扇形图数据
   * @url admin/analytics/store/sys/getChartData 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} store_id 门店ID（可选）
   * @params {String} theme_id 主题ID（可选）
   * @params {String} region 区域名称（可选）
   * @params {String} timeRange 时间范围类型（可选）
   * @params {Array} dateRange 日期范围（可选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let res = { code: 0, msg: "" };

    // 业务逻辑开始-----------------------------------------------------------
    let { store_id, theme_id, region, timeRange, dateRange, excludedThemes } = data;
    console.log("getChartData 接收到的数据:", data);

    try {
      // 引入权限检查工具
      const permissionChecker = require('../../../common/sys/checkStorePermission');

      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo, store_id);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }

      let { accessibleStores } = permissionResult;

      // 构建场次查询条件（使用权限检查工具）
      let sessionWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 10000,
          whereJson: {
            region: region
          },
          fieldJson: {
            _id: true
          }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);

          // 如果已经有门店ID限制，取交集
          if (sessionWhereJson.store_id) {
            if (sessionWhereJson.store_id.$in) {
              // 如果是数组形式，取交集
              sessionWhereJson.store_id = db.command.in(
                sessionWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              // 如果是单个门店ID，检查是否在区域内
              if (regionStoreIds.includes(sessionWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                // 门店不在指定区域内，设置为空结果
                sessionWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            // 没有门店ID限制，直接使用区域门店
            sessionWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          // 该区域下没有门店，设置为空结果
          sessionWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        sessionWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        sessionWhereJson.theme_id = db.command.nin(excludedThemes);
        // 如果同时指定了theme_id和excludedThemes，需要特殊处理
        if (theme_id) {
          sessionWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（转换为字符串格式）
      if (dateRange && dateRange.length === 2) {
        let startDate = new Date(dateRange[0]).toISOString().split('T')[0]; // YYYY-MM-DD
        let endDate = new Date(dateRange[1]).toISOString().split('T')[0]; // YYYY-MM-DD
        console.log("图表数据应用日期筛选:", { startDate, endDate });
        sessionWhereJson.session_date = _.gte(startDate).lte(endDate);
      }

      console.log("图表数据查询条件:", sessionWhereJson);

      // 使用聚合查询优化性能，按门店分组统计数据
      console.log("开始聚合查询图表数据...");
      let chartAggregateResult = await vk.baseDao.aggregate({
        dbName: "escape-sessions",
        pipeline: [
          { $match: sessionWhereJson },
          {
            $group: {
              _id: {
                store_id: "$store_id",
                store_name: "$store_name",
                theme_id: "$theme_id",
                theme_name: "$theme_name"
              },
              sessionCount: { $sum: 1 },
              playerCount: {
                $sum: {
                  $add: [
                    { $ifNull: ["$male_count", 0] },
                    { $ifNull: ["$female_count", 0] }
                  ]
                }
              },
              revenue: {
                $sum: {
                  $add: [
                    { $ifNull: ["$booking_amount", 0] },
                    { $ifNull: ["$tuangou_amount", 0] },
                    { $ifNull: ["$kaitianbao_amount", 0] },
                    { $ifNull: ["$wechat_amount", 0] },
                    { $ifNull: ["$cash_amount", 0] },
                    { $ifNull: ["$douyin_amount", 0] },
                    { $ifNull: ["$koubei_amount", 0] }
                  ]
                }
              },
              // 收集客户来源、性别、类型数据用于分布统计
              sources: { $push: "$customer_source" },
              genders: { $push: { male: "$male_count", female: "$female_count" } },
              types: { $push: "$customer_type" }
            }
          }
        ]
      });
      console.log(`聚合查询完成，获取到 ${chartAggregateResult && chartAggregateResult.data ? chartAggregateResult.data.length : 0} 组数据`);

      // 构建定金查询条件（使用权限检查工具）
      let depositWhereJson = permissionChecker.buildStoreWhereCondition(db, accessibleStores, store_id);

      // 如果有区域筛选，需要先查询该区域下的门店（复用之前的逻辑）
      if (region) {
        let regionStores = await vk.baseDao.selects({
          dbName: "escape-stores",
          pageIndex: 1,
          pageSize: 10000,
          whereJson: {
            region: region
          },
          fieldJson: {
            _id: true
          }
        });

        if (regionStores && regionStores.rows && regionStores.rows.length > 0) {
          let regionStoreIds = regionStores.rows.map(store => store._id);

          // 如果已经有门店ID限制，取交集
          if (depositWhereJson.store_id) {
            if (depositWhereJson.store_id.$in) {
              // 如果是数组形式，取交集
              depositWhereJson.store_id = db.command.in(
                depositWhereJson.store_id.$in.filter(id => regionStoreIds.includes(id))
              );
            } else {
              // 如果是单个门店ID，检查是否在区域内
              if (regionStoreIds.includes(depositWhereJson.store_id)) {
                // 保持原有的门店ID
              } else {
                // 门店不在指定区域内，设置为空结果
                depositWhereJson.store_id = db.command.in([]);
              }
            }
          } else {
            // 没有门店ID限制，直接使用区域门店
            depositWhereJson.store_id = db.command.in(regionStoreIds);
          }
        } else {
          // 该区域下没有门店，设置为空结果
          depositWhereJson.store_id = db.command.in([]);
        }
      }

      if (theme_id) {
        depositWhereJson.theme_id = theme_id;
      }

      // 如果有排除主题列表，添加排除条件
      if (excludedThemes && excludedThemes.length > 0) {
        depositWhereJson.theme_id = db.command.nin(excludedThemes);
        // 如果同时指定了theme_id和excludedThemes，需要特殊处理
        if (theme_id) {
          depositWhereJson.theme_id = db.command.and([
            db.command.eq(theme_id),
            db.command.nin(excludedThemes)
          ]);
        }
      }

      // 如果有日期范围，添加日期筛选（使用收定金日期）
      if (dateRange && dateRange.length === 2) {
        console.log("定金数据应用日期筛选:", dateRange);
        depositWhereJson.deposit_date = _.gte(dateRange[0]).lte(dateRange[1]);
      }

      console.log("定金数据查询条件:", depositWhereJson);

      // 获取所有定金数据
      console.log("开始获取定金数据...");
      let allDeposits = await vk.baseDao.selects({
        dbName: "escape-deposits",
        whereJson: depositWhereJson,
        pageSize: 10000 // 查询所有数据
      });
      console.log(`获取到 ${allDeposits && allDeposits.rows ? allDeposits.rows.length : 0} 条定金数据`);

      // 获取门店信息（根据权限限制）
      let storeWhereJson = {};
      if (accessibleStores !== null) {
        storeWhereJson._id = db.command.in(accessibleStores);
      }

      let storeList = await vk.baseDao.selects({
        dbName: "escape-stores",
        whereJson: storeWhereJson,
        pageSize: 10000 // 查询所有数据
      });

      // 获取主题信息（根据权限限制）
      let themeWhereJson = {};
      if (accessibleStores !== null) {
        themeWhereJson.store_id = db.command.in(accessibleStores);
      }

      let themeList = await vk.baseDao.selects({
        dbName: "escape-themes",
        whereJson: themeWhereJson,
        pageSize: 10000 // 查询所有数据
      });

      // 创建门店和主题的映射
      let storeMap = {};
      let themeMap = {};

      if (storeList && storeList.rows) {
        storeList.rows.forEach(store => {
          storeMap[store._id] = store;
        });
      }

      if (themeList && themeList.rows) {
        themeList.rows.forEach(theme => {
          themeMap[theme._id] = theme;
        });
      }

      // 初始化统计数据
      let sessionsByStore = {};  // 按门店统计场次
      let playersByStore = {};   // 按门店统计人数
      let revenueByStore = {};   // 按门店统计金额

      let sessionsByTheme = {};  // 按主题统计场次
      let playersByTheme = {};   // 按主题统计人数
      let revenueByTheme = {};   // 按主题统计金额

      let sessionsByCombo = {};  // 按主题-门店组合统计场次
      let playersByCombo = {};   // 按主题-门店组合统计人数
      let revenueByCombo = {};   // 按主题-门店组合统计金额

      // 玩家分布统计
      let sourceStats = {};      // 客户来源统计
      let genderStats = {};      // 性别统计
      let typeStats = {};        // 人群类型统计

      // 处理聚合查询结果
      if (chartAggregateResult && chartAggregateResult.data && chartAggregateResult.data.length > 0) {
        chartAggregateResult.data.forEach((item) => {
          let storeId = item._id.store_id;
          let storeName = item._id.store_name || '未知门店';
          let themeId = item._id.theme_id;
          let themeName = item._id.theme_name || '未知主题';
          let comboName = themeName + '-' + storeName; // 主题名称-门店名称

          let sessionCount = item.sessionCount || 0;
          let playerCount = item.playerCount || 0;
          let revenue = (item.revenue || 0) / 100; // 转换为元

          // 按门店统计
          if (!sessionsByStore[storeName]) {
            sessionsByStore[storeName] = 0;
            playersByStore[storeName] = 0;
            revenueByStore[storeName] = 0;
          }
          sessionsByStore[storeName] += sessionCount;
          playersByStore[storeName] += playerCount;
          revenueByStore[storeName] += revenue;

          // 按主题统计
          if (!sessionsByTheme[themeName]) {
            sessionsByTheme[themeName] = 0;
            playersByTheme[themeName] = 0;
            revenueByTheme[themeName] = 0;
          }
          sessionsByTheme[themeName] += sessionCount;
          playersByTheme[themeName] += playerCount;
          revenueByTheme[themeName] += revenue;

          // 按主题-门店组合统计
          if (!sessionsByCombo[comboName]) {
            sessionsByCombo[comboName] = 0;
            playersByCombo[comboName] = 0;
            revenueByCombo[comboName] = 0;
          }
          sessionsByCombo[comboName] += sessionCount;
          playersByCombo[comboName] += playerCount;
          revenueByCombo[comboName] += revenue;

          // 统计客户来源分布
          if (item.sources && Array.isArray(item.sources)) {
            item.sources.forEach(source => {
              let sourceKey = source || '未知来源';
              if (!sourceStats[sourceKey]) {
                sourceStats[sourceKey] = 0;
              }
              sourceStats[sourceKey] += 1; // 按场次计算，不是按人数
            });
          }

          // 统计性别分布
          if (item.genders && Array.isArray(item.genders)) {
            item.genders.forEach(gender => {
              let maleCount = gender.male || 0;
              let femaleCount = gender.female || 0;

              if (maleCount > 0) {
                if (!genderStats['男性']) {
                  genderStats['男性'] = 0;
                }
                genderStats['男性'] += maleCount;
              }

              if (femaleCount > 0) {
                if (!genderStats['女性']) {
                  genderStats['女性'] = 0;
                }
                genderStats['女性'] += femaleCount;
              }
            });
          }

          // 统计人群类型
          if (item.types && Array.isArray(item.types)) {
            item.types.forEach(type => {
              let customerType = type || '未知类型';
              if (!typeStats[customerType]) {
                typeStats[customerType] = 0;
              }
              typeStats[customerType] += 1; // 按场次计算，不是按人数
            });
          }
        });
      }

      // 统计定金数据
      if (allDeposits && allDeposits.rows && allDeposits.rows.length > 0) {
        console.log('开始统计定金数据...');
        allDeposits.rows.forEach((deposit) => {
          // 获取门店信息
          let store = storeMap[deposit.store_id];
          let storeName = store ? store.store_name : '未知门店';

          // 计算定金收入（以分为单位，需要除以100转换为元）
          let depositAmount = 0;

          // 检查多渠道支付信息
          if (deposit.payment_channels && Array.isArray(deposit.payment_channels)) {
            // 多渠道支付：累加所有渠道的金额
            deposit.payment_channels.forEach(paymentChannel => {
              depositAmount += paymentChannel.amount || 0;
            });
          } else {
            // 单一渠道支付：使用原有逻辑
            depositAmount = deposit.deposit_amount || 0;
          }

          let depositRevenue = depositAmount / 100;

          console.log(`定金记录: 门店=${storeName}, 金额=${depositRevenue}元`);

          // 按门店统计定金收入
          if (!revenueByStore[storeName]) {
            revenueByStore[storeName] = 0;
          }
          revenueByStore[storeName] += depositRevenue;

          // 如果有主题信息，也按主题统计定金收入
          if (deposit.theme_id) {
            let theme = themeMap[deposit.theme_id];
            let themeName = theme ? theme.theme_name : '未知主题';
            let comboName = themeName + '-' + storeName; // 保持与场次统计一致的格式

            // 按主题统计
            if (!revenueByTheme[themeName]) {
              revenueByTheme[themeName] = 0;
            }
            revenueByTheme[themeName] += depositRevenue;

            // 按主题-门店组合统计
            if (!revenueByCombo[comboName]) {
              revenueByCombo[comboName] = 0;
            }
            revenueByCombo[comboName] += depositRevenue;
          }
        });
        console.log('定金数据统计完成');
      }

      // 转换为ECharts需要的数据格式
      let sessionsChartData = [];
      let playersChartData = [];
      let revenueChartData = [];

      // 创建门店名称到ID的映射
      let storeNameToIdMap = {};
      if (storeList && storeList.rows) {
        storeList.rows.forEach(store => {
          storeNameToIdMap[store.store_name] = store._id;
        });
      }

      console.log('门店名称到ID映射:', storeNameToIdMap);
      console.log('按门店统计的数据:', sessionsByStore);

      // 使用门店的格式显示
      Object.keys(sessionsByStore).forEach(storeName => {
        sessionsChartData.push({
          name: storeName,
          value: sessionsByStore[storeName],
          store_id: storeNameToIdMap[storeName] || null
        });
        playersChartData.push({
          name: storeName,
          value: playersByStore[storeName],
          store_id: storeNameToIdMap[storeName] || null
        });
        revenueChartData.push({
          name: storeName,
          value: Math.round(revenueByStore[storeName] * 100) / 100, // 保留两位小数
          store_id: storeNameToIdMap[storeName] || null
        });
      });

      // 转换玩家分布数据为数组格式
      let sourceDistribution = Object.keys(sourceStats).map(key => ({
        name: key,
        value: sourceStats[key]
      }));

      let genderDistribution = Object.keys(genderStats).map(key => ({
        name: key,
        value: genderStats[key]
      }));

      let typeDistribution = Object.keys(typeStats).map(key => ({
        name: key,
        value: typeStats[key]
      }));

      res.data = {
        sessionsChart: sessionsChartData,
        playersChart: playersChartData,
        revenueChart: revenueChartData,
        sourceDistribution: sourceDistribution,
        genderDistribution: genderDistribution,
        typeDistribution: typeDistribution
      };

    } catch (err) {
      return { code: -1, msg: "扇形图数据获取失败：" + err.message };
    }

    return res;
  },
};
