[{"IndexName": "session_date_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "session_date", "Direction": "-1", "Type": "timestamp"}], "MgoIsUnique": false}}, {"IndexName": "store_id_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "store_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}, {"IndexName": "theme_id_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "theme_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}, {"IndexName": "store_session_date_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "store_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "session_date", "Direction": "-1", "Type": "timestamp"}], "MgoIsUnique": false}}, {"IndexName": "theme_session_date_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "theme_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "session_date", "Direction": "-1", "Type": "timestamp"}], "MgoIsUnique": false}}, {"IndexName": "store_theme_date_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "store_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "theme_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "session_date", "Direction": "-1", "Type": "timestamp"}], "MgoIsUnique": false}}, {"IndexName": "session_time_id_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "session_time_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}, {"IndexName": "_add_time_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "-1", "Type": "long"}], "MgoIsUnique": false}}]