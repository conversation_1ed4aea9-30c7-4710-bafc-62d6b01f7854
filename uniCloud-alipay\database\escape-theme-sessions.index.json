[{"IndexName": "theme_id_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "theme_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}, {"IndexName": "is_active_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "is_active", "Direction": "1", "Type": "bool"}], "MgoIsUnique": false}}, {"IndexName": "theme_active_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "theme_id", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "is_active", "Direction": "1", "Type": "bool"}], "MgoIsUnique": false}}, {"IndexName": "sort_order_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort_order", "Direction": "1", "Type": "int"}], "MgoIsUnique": false}}, {"IndexName": "create_time_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "create_time", "Direction": "-1", "Type": "long"}], "MgoIsUnique": false}}]