module.exports = {
  /**
   * 保存预设场次数据（新增或更新）
   * @url admin/session/sys/savePresetSession 前端调用的url参数地址
   * data 请求参数 说明
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { vk, db, _ } = util;
    let res = { code: 0, msg: '' };
    
    // 业务逻辑开始-----------------------------------------------------------
    let {
      _id,
      store_id,
      theme_id,
      session_date,
      session_time_id,
      session_status = '未预约',
      booking_phone = '',
      player_count = 0,
      male_count = 0,
      female_count = 0,
      customer_source = '',
      customer_type = '',
      booking_count = 0,
      booking_amount = 0,
      tuangou_count = 0,
      tuangou_amount = 0,
      kaitianbao_count = 0,
      kaitianbao_amount = 0,
      wechat_count = 0,
      wechat_amount = 0,
      cash_count = 0,
      cash_amount = 0,
      douyin_count = 0,
      douyin_amount = 0,
      koubei_count = 0,
      koubei_amount = 0,
      remarks = ''
    } = data;
    
    try {
      // 引入权限检查工具
      const permissionChecker = require('../../common/sys/checkStorePermission');
      
      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }
      
      let { accessibleStores } = permissionResult;
      
      // 检查门店权限
      if (accessibleStores !== null && !accessibleStores.includes(store_id)) {
        return { code: -1, msg: '无权限操作该门店的场次数据' };
      }
      
      // 参数验证
      if (!store_id || !theme_id || !session_date || !session_time_id) {
        return { code: -1, msg: '缺少必要参数' };
      }
      
      // 获取门店和主题信息
      let [storeInfo, themeInfo, sessionTimeInfo] = await Promise.all([
        vk.baseDao.findById({
          dbName: "escape-stores",
          id: store_id
        }),
        vk.baseDao.findById({
          dbName: "escape-themes",
          id: theme_id
        }),
        vk.baseDao.findById({
          dbName: "escape-theme-sessions",
          id: session_time_id
        })
      ]);
      
      if (!storeInfo) {
        return { code: -1, msg: '门店不存在' };
      }
      if (!themeInfo) {
        return { code: -1, msg: '主题不存在' };
      }
      if (!sessionTimeInfo) {
        return { code: -1, msg: '预设场次时间不存在' };
      }
      

      
      // 确保日期格式正确（保持字符串格式，符合数据库表结构）
      let sessionDateStr = session_date; // 保持原始的 YYYY-MM-DD 格式
      
      // 检查是否已存在该场次数据
      let existingSession = null;
      if (_id && !_id.startsWith('virtual_')) {
        // 如果有真实ID，查找现有记录
        existingSession = await vk.baseDao.findById({
          dbName: "escape-sessions",
          id: _id
        });
      } else {
        // 查找是否已有该预设场次的数据
        let sessionsResult = await vk.baseDao.selects({
          dbName: "escape-sessions",
          whereJson: {
            store_id,
            theme_id,
            session_date: sessionDateStr,
            session_time_id
          },
          pageSize: 1
        });
        let sessions = sessionsResult.rows || [];
        existingSession = sessions.length > 0 ? sessions[0] : null;
      }
      
      // 准备保存的数据
      let sessionData = {
        store_id,
        store_name: storeInfo.store_name,
        theme_id,
        theme_name: themeInfo.theme_name,
        session_date: sessionDateStr,
        session_time: sessionTimeInfo.session_time,
        session_time_id,
        session_status,
        booking_phone,
        player_count: parseInt(player_count) || 0,
        male_count: parseInt(male_count) || 0,
        female_count: parseInt(female_count) || 0,
        customer_source,
        customer_type,
        booking_count: parseInt(booking_count) || 0,
        booking_amount: parseFloat(booking_amount) || 0,
        tuangou_count: parseInt(tuangou_count) || 0,
        tuangou_amount: parseFloat(tuangou_amount) || 0,
        kaitianbao_count: parseInt(kaitianbao_count) || 0,
        kaitianbao_amount: parseFloat(kaitianbao_amount) || 0,
        wechat_count: parseInt(wechat_count) || 0,
        wechat_amount: parseFloat(wechat_amount) || 0,
        cash_count: parseInt(cash_count) || 0,
        cash_amount: parseFloat(cash_amount) || 0,
        douyin_count: parseInt(douyin_count) || 0,
        douyin_amount: parseFloat(douyin_amount) || 0,
        koubei_count: parseInt(koubei_count) || 0,
        koubei_amount: parseFloat(koubei_amount) || 0,
        remarks,
        update_time: new Date()
      };
      
      if (existingSession) {
        // 更新现有记录
        let updateRes = await vk.baseDao.updateById({
          dbName: "escape-sessions",
          id: existingSession._id,
          dataJson: sessionData
        });
        
        if (updateRes) {
          res.data = { _id: existingSession._id };
          res.msg = "更新成功";
        } else {
          res.code = -1;
          res.msg = "更新失败";
        }
      } else {
        // 新增记录
        sessionData.create_time = new Date();
        sessionData._add_time = new Date();
        
        let addRes = await vk.baseDao.add({
          dbName: "escape-sessions",
          dataJson: sessionData
        });
        
        if (addRes) {
          res.data = { _id: addRes.id };
          res.msg = "保存成功";
        } else {
          res.code = -1;
          res.msg = "保存失败";
        }
      }
      
    } catch (error) {
      console.error('保存预设场次失败：', error);
      res.code = -1;
      res.msg = `保存失败: ${error.message}`;
    }
    
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
}
