{"bsonType": "object", "required": ["store_id", "theme_id", "session_date", "start_time", "end_time"], "properties": {"_id": {"description": "ID，系统自动生成"}, "store_id": {"bsonType": "string", "title": "门店ID", "description": "所属门店ID，必填"}, "store_name": {"bsonType": "string", "title": "门店名称", "description": "所属门店名称"}, "theme_id": {"bsonType": "string", "title": "主题ID", "description": "游戏主题ID，必填"}, "theme_name": {"bsonType": "string", "title": "主题名称", "description": "游戏主题名称"}, "session_date": {"bsonType": "timestamp", "title": "场次日期", "description": "场次日期时间戳，必填"}, "start_time": {"bsonType": "string", "title": "开始时间", "description": "场次开始时间，格式：HH:mm，必填"}, "end_time": {"bsonType": "string", "title": "结束时间", "description": "场次结束时间，格式：HH:mm，必填"}, "session_time": {"bsonType": "array", "title": "场次时间", "description": "场次时间范围，格式：[开始时间, 结束时间]，如：['18:50', '20:00']", "items": {"bsonType": "string"}, "minItems": 2, "maxItems": 2}, "session_time_id": {"bsonType": "string", "title": "预设场次时间ID", "description": "关联的预设场次时间ID"}, "customer_source": {"bsonType": "string", "title": "客户来源", "description": "客户来源渠道", "enum": ["外联", "微信", "美团", "点评", "散客", "传单", "抖音", "胶己人", "朋友介绍", "楼梯广告"]}, "customer_type": {"bsonType": "string", "title": "人群类型", "description": "客户人群类型", "enum": ["大学生", "中学生", "汕大", "上班族", "医护", "教师", "亲子"]}, "player_count": {"bsonType": "int", "title": "参与人数", "description": "实际参与游戏的人数", "minimum": 1, "maximum": 20}, "session_status": {"bsonType": "int", "title": "场次状态", "description": "1:已预约 2:进行中 3:已完成 4:已取消", "enum": [1, 2, 3, 4], "default": 1}, "booking_phone": {"bsonType": "string", "title": "预约电话", "description": "客户预约联系电话", "maxLength": 20}, "customer_name": {"bsonType": "string", "title": "客户姓名", "description": "主要联系人姓名", "maxLength": 50}, "total_price": {"bsonType": "double", "title": "总价格", "description": "本场次总价格", "minimum": 0}, "paid_amount": {"bsonType": "double", "title": "已付金额", "description": "客户已支付金额", "minimum": 0, "default": 0}, "payment_status": {"bsonType": "int", "title": "支付状态", "description": "1:未支付 2:部分支付 3:已支付 4:已退款", "enum": [1, 2, 3, 4], "default": 1}, "male_count": {"bsonType": "int", "title": "男性人数", "description": "男性玩家人数", "minimum": 0, "default": 0}, "female_count": {"bsonType": "int", "title": "女性人数", "description": "女性玩家人数", "minimum": 0, "default": 0}, "booking_count": {"bsonType": "int", "title": "预约人数", "description": "预约渠道人数", "minimum": 0, "default": 0}, "booking_amount": {"bsonType": "int", "title": "预约金额", "description": "预约渠道金额（分）", "minimum": 0, "default": 0}, "tuangou_count": {"bsonType": "int", "title": "团购人数", "description": "团购渠道人数", "minimum": 0, "default": 0}, "tuangou_amount": {"bsonType": "int", "title": "团购金额", "description": "团购渠道金额（分）", "minimum": 0, "default": 0}, "kaitianbao_count": {"bsonType": "int", "title": "收款码人数", "description": "收款码渠道人数", "minimum": 0, "default": 0}, "kaitianbao_amount": {"bsonType": "int", "title": "收款码金额", "description": "收款码渠道金额（分）", "minimum": 0, "default": 0}, "wechat_count": {"bsonType": "int", "title": "微信人数", "description": "微信渠道人数", "minimum": 0, "default": 0}, "wechat_amount": {"bsonType": "int", "title": "微信金额", "description": "微信渠道金额（分）", "minimum": 0, "default": 0}, "cash_count": {"bsonType": "int", "title": "现金人数", "description": "现金渠道人数", "minimum": 0, "default": 0}, "cash_amount": {"bsonType": "int", "title": "现金金额", "description": "现金渠道金额（分）", "minimum": 0, "default": 0}, "douyin_count": {"bsonType": "int", "title": "抖音人数", "description": "抖音渠道人数", "minimum": 0, "default": 0}, "douyin_amount": {"bsonType": "int", "title": "抖音金额", "description": "抖音渠道金额（分）", "minimum": 0, "default": 0}, "koubei_count": {"bsonType": "int", "title": "支付宝人数", "description": "支付宝渠道人数", "minimum": 0, "default": 0}, "koubei_amount": {"bsonType": "int", "title": "支付宝金额", "description": "支付宝渠道金额（分）", "minimum": 0, "default": 0}, "game_result": {"bsonType": "string", "title": "游戏结果", "description": "成功/失败/未完成", "enum": ["成功", "失败", "未完成"], "default": "未完成"}, "completion_time": {"bsonType": "int", "title": "完成时间", "description": "游戏完成用时，单位：分钟"}, "customer_rating": {"bsonType": "double", "title": "客户评分", "description": "客户对本次游戏的评分，1-5分", "minimum": 1, "maximum": 5}, "remarks": {"bsonType": "string", "title": "备注", "description": "场次备注信息", "maxLength": 500}, "staff_id": {"bsonType": "string", "title": "负责员工ID", "description": "负责本场次的员工ID"}, "staff_name": {"bsonType": "string", "title": "负责员工", "description": "负责本场次的员工姓名"}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间"}, "update_time": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间"}}}