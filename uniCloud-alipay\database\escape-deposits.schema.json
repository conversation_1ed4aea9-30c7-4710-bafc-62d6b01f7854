{"bsonType": "object", "required": ["store_id", "theme_id", "session_date", "session_time_id", "deposit_date", "deposit_amount", "payment_method"], "properties": {"_id": {"description": "ID，系统自动生成"}, "store_id": {"bsonType": "string", "title": "门店ID", "description": "所属门店ID，必填"}, "store_name": {"bsonType": "string", "title": "门店名称", "description": "所属门店名称"}, "theme_id": {"bsonType": "string", "title": "主题ID", "description": "游戏主题ID，必填"}, "theme_name": {"bsonType": "string", "title": "主题名称", "description": "游戏主题名称"}, "session_date": {"bsonType": "timestamp", "title": "场次日期", "description": "场次日期，必填"}, "session_time_id": {"bsonType": "string", "title": "场次时间ID", "description": "预设场次时间ID，必填"}, "session_time": {"bsonType": "array", "title": "场次时间", "description": "场次时间范围，格式：[开始时间, 结束时间]，如：['14:00', '15:00']", "items": {"bsonType": "string"}, "minItems": 2, "maxItems": 2}, "deposit_date": {"bsonType": "timestamp", "title": "收定金日期", "description": "收取定金的日期，必填"}, "payment_channels": {"bsonType": "array", "title": "多渠道收款信息", "description": "支持多个收款渠道的详细信息，可选", "items": {"bsonType": "object", "properties": {"payment_method": {"bsonType": "string", "title": "收款方式", "enum": ["预约", "团购", "收款码", "微信", "现金", "抖音", "支付宝"]}, "amount": {"bsonType": "int", "title": "金额", "description": "该渠道的收款金额，单位：分", "minimum": 0}}, "required": ["payment_method", "amount"]}}, "remarks": {"bsonType": "string", "title": "备注", "description": "定金记录备注信息", "maxLength": 500}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间"}, "update_time": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间"}, "_add_time_str": {"bsonType": "string", "title": "创建时间字符串", "description": "创建时间的字符串格式，如：2025-07-31 20:27:34"}}}