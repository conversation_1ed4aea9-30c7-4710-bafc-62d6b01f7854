{"name": "router", "version": "1.0.0", "description": "【开箱即用】vk-uniCloud-router - 云函数路由模式 - uniCloud开发框架 - 已集成uni-id", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "vk-unicloud": "file:../../../uni_modules/vk-unicloud/uniCloud/cloudfunctions/common/vk-unicloud"}, "private": true, "cloudfunction-config": {"concurrency": 1, "memorySize": 512, "path": "", "timeout": 180, "triggers": [], "runtime": "Nodejs18", "keepRunningAfterReturn": false}, "extensions": {}, "origin-plugin-dev-name": "vk-unicloud-admin", "origin-plugin-version": "1.20.2", "plugin-dev-name": "vk-unicloud-admin", "plugin-version": "1.20.2"}