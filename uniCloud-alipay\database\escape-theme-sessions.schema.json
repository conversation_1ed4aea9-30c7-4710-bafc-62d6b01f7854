{"bsonType": "object", "required": ["theme_id", "session_time"], "properties": {"_id": {"description": "ID，系统自动生成"}, "theme_id": {"bsonType": "string", "title": "主题ID", "description": "所属主题ID，必填"}, "theme_name": {"bsonType": "string", "title": "主题名称", "description": "所属主题名称"}, "session_time": {"bsonType": "array", "title": "场次时间", "description": "场次时间范围，格式：[开始时间, 结束时间]，如：['09:00', '10:30']，必填", "items": {"bsonType": "string"}, "minItems": 2, "maxItems": 2}, "is_active": {"bsonType": "bool", "title": "是否启用", "description": "是否启用该场次时间", "default": true}, "sort_order": {"bsonType": "int", "title": "排序", "description": "场次时间排序，数字越小越靠前", "default": 0}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间"}, "update_time": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间"}, "_add_time": {"bsonType": "timestamp", "title": "添加时间", "description": "记录添加时间戳"}, "_add_time_str": {"bsonType": "string", "title": "添加时间字符串", "description": "添加时间的字符串格式，如：2025-07-22 22:15:31"}}}