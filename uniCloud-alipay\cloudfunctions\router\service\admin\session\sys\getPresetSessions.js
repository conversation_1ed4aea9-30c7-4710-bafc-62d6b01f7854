module.exports = {
  /**
   * 获取某天的预设场次列表（虚拟数据+实际数据）
   * @url admin/session/sys/getPresetSessions 前端调用的url参数地址
   * data 请求参数 说明
   * @params {String} session_date 场次日期 YYYY-MM-DD
   * @params {String} store_id 门店ID（可选，用于筛选）
   * @params {String} theme_id 主题ID（可选，用于筛选）
   * @params {String} session_status 场次状态（可选，用于筛选）
   */
  main: async (event) => {
    let { data = {}, userInfo, util } = event;
    let { vk, db, _ } = util;
    let res = { code: 0, msg: '' };

    // 业务逻辑开始-----------------------------------------------------------
    let { session_date, store_id, theme_id, session_status } = data;
    
    // 如果没有传入日期，默认使用今天
    if (!session_date) {
      session_date = vk.pubfn.timeFormat(new Date(), "yyyy-MM-dd");
    }
    
    try {
      // 引入权限检查工具
      const permissionChecker = require('../../common/sys/checkStorePermission');
      
      // 检查用户门店访问权限
      let permissionResult = permissionChecker.checkUserStorePermission(userInfo);
      if (!permissionResult.success) {
        return { code: permissionResult.code, msg: permissionResult.msg };
      }
      
      let { accessibleStores } = permissionResult;
      
      // 构建门店查询条件
      let storeWhereJson = {};
      if (accessibleStores !== null) {
        // 非超级管理员：限制为可访问的门店
        storeWhereJson._id = _.in(accessibleStores);
      }
      if (store_id) {
        storeWhereJson._id = store_id;
      }
      
      // 1. 获取所有可访问的门店
      let storesResult = await vk.baseDao.selects({
        dbName: "escape-stores",
        whereJson: storeWhereJson,
        pageSize: 10000
      });

      let stores = storesResult.rows || [];
      if (stores.length === 0) {
        return {
          code: 0,
          msg: '查询成功',
          data: []
        };
      }
      
      let allPresetSessions = [];
      
      // 2. 遍历每个门店，获取其主题和预设场次
      for (let store of stores) {
        // 构建主题查询条件
        let themeWhereJson = {
          store_id: store._id,
          theme_status: 1 // 只获取启用的主题
        };

        // 如果指定了主题ID，则只查询该主题
        if (theme_id) {
          themeWhereJson._id = theme_id;
        }

        // 获取门店下的主题
        let themesResult = await vk.baseDao.selects({
          dbName: "escape-themes",
          whereJson: themeWhereJson,
          pageSize: 10000
        });

        let themes = themesResult.rows || [];
        
        // 遍历每个主题，获取预设场次
        for (let theme of themes) {
          // 获取主题的预设场次时间
          let themeSessionsResult = await vk.baseDao.selects({
            dbName: "escape-theme-sessions",
            whereJson: {
              theme_id: theme._id,
              is_active: true
            },
            pageSize: 10000
          });

          let themeSessions = themeSessionsResult.rows || [];
          
          // 按时间排序
          themeSessions.sort((a, b) => {
            let timeA = a.session_time && a.session_time[0] ? a.session_time[0] : '';
            let timeB = b.session_time && b.session_time[0] ? b.session_time[0] : '';
            return timeA.localeCompare(timeB);
          });
          
          // 3. 查询该日期该主题的实际场次数据
          let actualSessionsResult = await vk.baseDao.selects({
            dbName: "escape-sessions",
            whereJson: {
              store_id: store._id,
              theme_id: theme._id,
              session_date: session_date
            },
            pageSize: 10000
          });

          let actualSessions = actualSessionsResult.rows || [];
          
          // 创建实际场次的映射（以session_time_id为key）
          let actualSessionMap = {};
          actualSessions.forEach(session => {
            if (session.session_time_id) {
              actualSessionMap[session.session_time_id] = session;
            }
          });
          
          // 4. 为每个预设场次创建虚拟数据或使用实际数据
          themeSessions.forEach(themeSession => {
            let sessionData;
            
            if (actualSessionMap[themeSession._id]) {
              // 如果有实际数据，使用实际数据
              sessionData = {
                ...actualSessionMap[themeSession._id],
                is_virtual: false // 标记为非虚拟数据
              };
            } else {
              // 创建虚拟数据
              sessionData = {
                _id: `virtual_${themeSession._id}_${session_date}`, // 虚拟ID
                store_id: store._id,
                store_name: store.store_name,
                theme_id: theme._id,
                theme_name: theme.theme_name,
                session_date: new Date(session_date + ' 00:00:00').getTime(),
                session_time: themeSession.session_time,
                session_time_id: themeSession._id,
                session_status: '未预约',
                booking_phone: '',
                player_count: 0,
                male_count: 0,
                female_count: 0,
                customer_source: '',
                customer_type: '',
                booking_count: 0,
                booking_amount: 0,
                tuangou_count: 0,
                tuangou_amount: 0,
                kaitianbao_count: 0,
                kaitianbao_amount: 0,
                wechat_count: 0,
                wechat_amount: 0,
                cash_count: 0,
                cash_amount: 0,
                douyin_count: 0,
                douyin_amount: 0,
                koubei_count: 0,
                koubei_amount: 0,
                remarks: '',
                is_virtual: true // 标记为虚拟数据
              };
            }
            
            allPresetSessions.push(sessionData);
          });
        }
      }
      
      // 5. 根据状态筛选（如果指定了状态）
      if (session_status) {
        allPresetSessions = allPresetSessions.filter(session => {
          return session.session_status === session_status;
        });
      }

      // 6. 按门店、主题、时间排序
      allPresetSessions.sort((a, b) => {
        // 先按门店名称排序
        if (a.store_name !== b.store_name) {
          return a.store_name.localeCompare(b.store_name);
        }
        // 再按主题名称排序
        if (a.theme_name !== b.theme_name) {
          return a.theme_name.localeCompare(b.theme_name);
        }
        // 最后按时间排序
        let timeA = a.session_time && a.session_time[0] ? a.session_time[0] : '';
        let timeB = b.session_time && b.session_time[0] ? b.session_time[0] : '';
        return timeA.localeCompare(timeB);
      });

      res.data = allPresetSessions;
      res.total = allPresetSessions.length;
      res.msg = "查询成功";
      
    } catch (error) {
      console.error('获取预设场次失败：', error);
      res.code = -1;
      res.msg = `获取预设场次失败: ${error.message}`;
      res.data = [];
    }
    
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
}
