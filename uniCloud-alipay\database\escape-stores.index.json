[{"IndexName": "_add_time_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "-1", "Type": "long"}], "MgoIsUnique": false}}, {"IndexName": "location_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "province", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "city", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}, {"Name": "district", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}, {"IndexName": "store_name_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "store_name", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": true}}, {"IndexName": "store_status_index", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "store_status", "Direction": "1", "Type": "<PERSON><PERSON><PERSON>"}], "MgoIsUnique": false}}]